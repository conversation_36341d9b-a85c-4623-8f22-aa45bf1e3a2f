// Smart Invoice Generator - JavaScript
// Handles form interactions, live preview, and PDF generation

let itemCounter = 1;

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

function initializeApp() {
    // Set default dates
    setDefaultDates();

    // Add event listeners for form inputs
    addFormEventListeners();

    // Add event listeners for buttons
    addButtonEventListeners();

    // Initialize navigation
    initializeNavigation();

    // Generate initial invoice number
    generateInvoiceNumber();

    // Initial preview update
    updatePreview();

    // Load initial data for other tabs
    loadInvoiceHistory();
    loadClients();
}

function setDefaultDates() {
    const today = new Date();
    const invoiceDateInput = document.getElementById('invoiceDate');
    const dueDateInput = document.getElementById('dueDate');
    
    // Set invoice date to today
    invoiceDateInput.value = today.toISOString().split('T')[0];
    
    // Set due date to 30 days from today
    const dueDate = new Date(today);
    dueDate.setDate(dueDate.getDate() + 30);
    dueDateInput.value = dueDate.toISOString().split('T')[0];
}

function addFormEventListeners() {
    // Business and client information
    const formInputs = [
        'businessName', 'businessEmail', 'clientName', 'clientEmail',
        'invoiceDate', 'dueDate', 'notes', 'bankName', 'accountNumber',
        'taxRate', 'signatureName'
    ];

    formInputs.forEach(inputId => {
        const element = document.getElementById(inputId);
        if (element) {
            element.addEventListener('input', updatePreview);
        }
    });

    // Invoice items event listeners
    addItemEventListeners();
}

function addItemEventListeners() {
    const itemsContainer = document.getElementById('invoiceItems');
    
    // Add event listeners to existing item inputs
    const itemInputs = itemsContainer.querySelectorAll('input[name="itemDescription[]"], input[name="itemQuantity[]"], input[name="itemPrice[]"]');
    itemInputs.forEach(input => {
        input.addEventListener('input', function() {
            calculateItemTotal(this.closest('.item-row'));
            updatePreview();
        });
    });
}

function addButtonEventListeners() {
    // Add item button
    document.getElementById('addItemBtn').addEventListener('click', addNewItem);
    
    // Generate PDF button
    document.getElementById('generatePdfBtn').addEventListener('click', generatePDF);
    
    // Send invoice button
    document.getElementById('sendInvoiceBtn').addEventListener('click', sendInvoice);
}

function generateInvoiceNumber() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    
    const invoiceNumber = `INV-${year}${month}${day}-${random}`;
    document.getElementById('previewInvoiceNumber').textContent = invoiceNumber;
    
    return invoiceNumber;
}

function addNewItem() {
    itemCounter++;
    const itemsContainer = document.getElementById('invoiceItems');
    
    const newItemRow = document.createElement('div');
    newItemRow.className = 'item-row';
    newItemRow.setAttribute('data-item', itemCounter);
    
    newItemRow.innerHTML = `
        <div class="item-input-group">
            <label>Description *</label>
            <input type="text" name="itemDescription[]" required>
        </div>
        <div class="item-input-group">
            <label>Quantity *</label>
            <input type="number" name="itemQuantity[]" min="1" value="1" required>
        </div>
        <div class="item-input-group">
            <label>Price ($) *</label>
            <input type="number" name="itemPrice[]" step="0.01" min="0" required>
        </div>
        <div class="item-input-group">
            <label>Total</label>
            <input type="text" class="item-total" readonly>
        </div>
        <button type="button" class="remove-item-btn" onclick="removeItem(${itemCounter})">×</button>
    `;
    
    // Insert before the add button
    const addButton = document.getElementById('addItemBtn');
    itemsContainer.insertBefore(newItemRow, addButton);
    
    // Add event listeners to new item inputs
    const newInputs = newItemRow.querySelectorAll('input[name="itemDescription[]"], input[name="itemQuantity[]"], input[name="itemPrice[]"]');
    newInputs.forEach(input => {
        input.addEventListener('input', function() {
            calculateItemTotal(this.closest('.item-row'));
            updatePreview();
        });
    });
    
    updatePreview();
}

function removeItem(itemId) {
    const itemRow = document.querySelector(`[data-item="${itemId}"]`);
    if (itemRow) {
        // Don't allow removing the last item
        const totalItems = document.querySelectorAll('.item-row').length;
        if (totalItems > 1) {
            itemRow.remove();
            updatePreview();
        } else {
            showStatusMessage('At least one item is required', 'error');
        }
    }
}

function calculateItemTotal(itemRow) {
    const quantityInput = itemRow.querySelector('input[name="itemQuantity[]"]');
    const priceInput = itemRow.querySelector('input[name="itemPrice[]"]');
    const totalInput = itemRow.querySelector('.item-total');
    
    const quantity = parseFloat(quantityInput.value) || 0;
    const price = parseFloat(priceInput.value) || 0;
    const total = quantity * price;
    
    totalInput.value = `$${total.toFixed(2)}`;
}

function updatePreview() {
    // Update business information
    const businessName = document.getElementById('businessName').value || 'Thynk Unlimited';
    const businessEmail = document.getElementById('businessEmail').value || '123 Anywhere St., Any City';
    document.getElementById('previewBusinessName').textContent = businessName;
    document.getElementById('previewClientEmail').textContent = businessEmail;

    // Update client information
    const clientName = document.getElementById('clientName').value || 'Richard Sanchez';
    document.getElementById('previewClientName').textContent = clientName;

    // Update bank information
    const bankName = document.getElementById('bankName').value || 'Borcele Bank';
    const accountNumber = document.getElementById('accountNumber').value || '0123 4567 8901';
    document.getElementById('previewBankName').textContent = bankName;
    document.getElementById('previewAccountName').textContent = businessName;
    document.getElementById('previewAccountNumber').textContent = accountNumber;

    // Update dates (using short format)
    const invoiceDate = document.getElementById('invoiceDate').value;
    const dueDate = document.getElementById('dueDate').value;
    document.getElementById('previewInvoiceDate').textContent = formatDateShort(invoiceDate) || '11.02.2030';
    document.getElementById('previewDueDate').textContent = formatDateShort(dueDate) || '11.03.2030';

    // Update tax rate
    const taxRate = document.getElementById('taxRate').value || '10';
    document.getElementById('previewTaxRate').textContent = taxRate;

    // Update signature
    const signatureName = document.getElementById('signatureName').value || 'Adeline Palmerston';
    document.getElementById('previewSignatureName').textContent = signatureName;

    // Update items table
    updateItemsPreview();

    // Update notes
    const notes = document.getElementById('notes').value || 'Additional notes will appear here...';
    document.getElementById('previewNotes').textContent = notes;
}

function updateItemsPreview() {
    const itemRows = document.querySelectorAll('.item-row');
    const previewTableBody = document.getElementById('previewItemsBody');

    // Clear existing preview items
    previewTableBody.innerHTML = '';

    let subtotal = 0;

    itemRows.forEach(row => {
        const description = row.querySelector('input[name="itemDescription[]"]').value || 'Brand consultation';
        const quantity = parseFloat(row.querySelector('input[name="itemQuantity[]"]').value) || 1;
        const price = parseFloat(row.querySelector('input[name="itemPrice[]"]').value) || 100;
        const total = quantity * price;

        subtotal += total;

        const previewRow = document.createElement('tr');
        previewRow.innerHTML = `
            <td>${description}</td>
            <td>${price.toFixed(0)}</td>
            <td>${quantity}</td>
            <td>$${total.toFixed(0)}</td>
        `;

        previewTableBody.appendChild(previewRow);
    });

    // Calculate tax and total
    const taxRate = parseFloat(document.getElementById('taxRate').value) || 10;
    const taxAmount = subtotal * (taxRate / 100);
    const finalTotal = subtotal + taxAmount;

    // Update totals in preview
    document.getElementById('previewSubtotal').textContent = subtotal.toFixed(0);
    document.getElementById('previewTotal').textContent = finalTotal.toFixed(0);
}

function formatDate(dateString) {
    if (!dateString) return 'Select date';
    
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

function showStatusMessage(message, type) {
    const statusDiv = document.getElementById('statusMessage');
    statusDiv.textContent = message;
    statusDiv.className = `status-message ${type}`;
    
    // Show the message
    statusDiv.classList.remove('hidden');
    
    // Hide after 5 seconds
    setTimeout(() => {
        statusDiv.classList.add('hidden');
    }, 5000);
}

// PDF Generation using jsPDF - Matching the provided invoice design
function generatePDF() {
    try {
        // Validate form data
        if (!validateForm()) {
            return;
        }

        showStatusMessage('Generating PDF...', 'success');

        // Get jsPDF from the global scope
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF();

        // Get form data
        const formData = getFormData();

        // Set up PDF styling to match the provided design
        const pageWidth = doc.internal.pageSize.width;
        const pageHeight = doc.internal.pageSize.height;
        const margin = 30;
        let yPosition = 40;

        // Header - Decorative line and INVOICE title (matching the design)
        doc.setLineWidth(0.5);
        doc.line(margin, yPosition, margin + 60, yPosition); // Decorative line

        doc.setFontSize(28);
        doc.setFont(undefined, 'normal');
        doc.text('I N V O I C E', pageWidth - margin - 80, yPosition + 5);
        yPosition += 50;

        // Left side - ISSUED TO section
        doc.setFontSize(10);
        doc.setFont(undefined, 'bold');
        doc.text('ISSUED TO:', margin, yPosition);
        yPosition += 8;

        doc.setFontSize(11);
        doc.setFont(undefined, 'normal');
        doc.text(formData.clientName, margin, yPosition);
        yPosition += 6;
        doc.text(formData.businessName, margin, yPosition); // Company name
        yPosition += 6;
        doc.text(formData.clientEmail, margin, yPosition); // Using email as address placeholder
        yPosition += 20;

        // Right side - Invoice details (matching the layout)
        const rightColumnX = pageWidth - margin - 80;
        let rightYPosition = yPosition - 42; // Align with ISSUED TO

        doc.setFontSize(10);
        doc.setFont(undefined, 'bold');
        doc.text('INVOICE NO:', rightColumnX, rightYPosition);
        doc.setFont(undefined, 'normal');
        doc.text(document.getElementById('previewInvoiceNumber').textContent.replace('INV-', ''), rightColumnX + 45, rightYPosition);
        rightYPosition += 8;

        doc.setFont(undefined, 'bold');
        doc.text('DATE:', rightColumnX, rightYPosition);
        doc.setFont(undefined, 'normal');
        doc.text(formatDateShort(formData.invoiceDate), rightColumnX + 45, rightYPosition);
        rightYPosition += 8;

        doc.setFont(undefined, 'bold');
        doc.text('DUE DATE:', rightColumnX, rightYPosition);
        doc.setFont(undefined, 'normal');
        doc.text(formatDateShort(formData.dueDate), rightColumnX + 45, rightYPosition);

        // PAY TO section (bank details)
        doc.setFontSize(10);
        doc.setFont(undefined, 'bold');
        doc.text('PAY TO:', margin, yPosition);
        yPosition += 8;

        doc.setFontSize(11);
        doc.setFont(undefined, 'normal');
        doc.text(formData.bankName || 'Borcele Bank', margin, yPosition);
        yPosition += 6;
        doc.text(`Account Name: ${formData.businessName}`, margin, yPosition);
        yPosition += 6;
        doc.text(`Account No.: ${formData.accountNumber || '0123 4567 8901'}`, margin, yPosition);
        yPosition += 30;

        // Items Table Header (matching the design exactly)
        const tableStartY = yPosition;
        const colWidths = [90, 35, 20, 35];
        const colPositions = [margin, margin + colWidths[0], margin + colWidths[0] + colWidths[1], margin + colWidths[0] + colWidths[1] + colWidths[2]];

        doc.setFontSize(10);
        doc.setFont(undefined, 'bold');
        doc.text('DESCRIPTION', colPositions[0], yPosition);
        doc.text('UNIT PRICE', colPositions[1], yPosition);
        doc.text('QTY', colPositions[2], yPosition);
        doc.text('TOTAL', colPositions[3], yPosition);
        yPosition += 8;

        // Draw line under header (matching the design)
        doc.setLineWidth(0.3);
        doc.line(margin, yPosition, pageWidth - margin, yPosition);
        yPosition += 12;

        // Items (matching the clean style)
        doc.setFont(undefined, 'normal');
        let subtotal = 0;

        formData.items.forEach(item => {
            const total = item.quantity * item.price;
            subtotal += total;

            // Clean item layout matching the design
            doc.text(item.description, colPositions[0], yPosition);
            doc.text(item.price.toString(), colPositions[1], yPosition);
            doc.text(item.quantity.toString(), colPositions[2], yPosition);
            doc.text(`$${total.toFixed(0)}`, colPositions[3], yPosition); // No decimals like in the design

            yPosition += 12; // Consistent spacing
        });

        yPosition += 10;

        // Totals section (matching the design layout)
        const totalsX = pageWidth - margin - 60;

        // Subtotal
        doc.setFontSize(10);
        doc.setFont(undefined, 'bold');
        doc.text('SUBTOTAL', margin, yPosition);
        doc.setFont(undefined, 'normal');
        doc.text(`$${subtotal.toFixed(0)}`, totalsX, yPosition);
        yPosition += 10;

        // Tax (using form value)
        const taxRatePercent = parseFloat(formData.taxRate) || 10;
        const taxRate = taxRatePercent / 100;
        const taxAmount = subtotal * taxRate;
        doc.setFont(undefined, 'normal');
        doc.text('Tax', totalsX - 20, yPosition);
        doc.text(`${taxRatePercent}%`, totalsX, yPosition);
        yPosition += 10;

        // Final Total
        const finalTotal = subtotal + taxAmount;
        doc.setFont(undefined, 'bold');
        doc.text('TOTAL', margin, yPosition);
        doc.text(`$${finalTotal.toFixed(0)}`, totalsX, yPosition);
        yPosition += 30;

        // Signature area (matching the design)
        yPosition += 20;
        doc.setFontSize(16);
        doc.setFont(undefined, 'normal');
        // Use signature name from form
        const signatureName = formData.signatureName || 'Adeline Palmerston';
        doc.text(signatureName, pageWidth - margin - 80, yPosition);

        // Add a line above signature
        doc.setLineWidth(0.3);
        doc.line(pageWidth - margin - 80, yPosition - 5, pageWidth - margin - 20, yPosition - 5);

        // Notes section (if any)
        if (formData.notes && formData.notes.trim()) {
            yPosition += 20;
            doc.setFontSize(10);
            doc.setFont(undefined, 'bold');
            doc.text('Notes:', margin, yPosition);
            yPosition += 8;

            doc.setFont(undefined, 'normal');
            const notesLines = doc.splitTextToSize(formData.notes, pageWidth - 2 * margin);
            doc.text(notesLines, margin, yPosition);
        }

        // Generate filename with timestamp
        const now = new Date();
        const timestamp = now.toISOString().split('T')[0].replace(/-/g, '_');
        const filename = `invoice_${timestamp}_${formData.clientName.replace(/\s+/g, '_').toLowerCase()}.pdf`;

        // Save the PDF
        doc.save(filename);

        showStatusMessage('PDF generated successfully!', 'success');

    } catch (error) {
        console.error('Error generating PDF:', error);
        showStatusMessage('Error generating PDF. Please try again.', 'error');
    }
}

// Helper function for short date format (matching the design)
function formatDateShort(dateString) {
    if (!dateString) return '';

    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();

    return `${day}.${month}.${year}`;
}

function getFormData() {
    const items = [];
    const itemRows = document.querySelectorAll('.item-row');

    itemRows.forEach(row => {
        const description = row.querySelector('input[name="itemDescription[]"]').value;
        const quantity = parseFloat(row.querySelector('input[name="itemQuantity[]"]').value) || 0;
        const price = parseFloat(row.querySelector('input[name="itemPrice[]"]').value) || 0;

        if (description && quantity > 0 && price >= 0) {
            items.push({ description, quantity, price });
        }
    });

    return {
        businessName: document.getElementById('businessName').value,
        businessEmail: document.getElementById('businessEmail').value,
        clientName: document.getElementById('clientName').value,
        clientEmail: document.getElementById('clientEmail').value,
        invoiceDate: document.getElementById('invoiceDate').value,
        dueDate: document.getElementById('dueDate').value,
        notes: document.getElementById('notes').value,
        bankName: document.getElementById('bankName').value,
        accountNumber: document.getElementById('accountNumber').value,
        taxRate: document.getElementById('taxRate').value,
        signatureName: document.getElementById('signatureName').value,
        items: items
    };
}

function validateForm() {
    const requiredFields = [
        'businessName', 'businessEmail', 'clientName', 'clientEmail',
        'invoiceDate', 'dueDate'
    ];

    for (let fieldId of requiredFields) {
        const field = document.getElementById(fieldId);
        if (!field.value.trim()) {
            showStatusMessage(`Please fill in the ${field.labels[0].textContent}`, 'error');
            field.focus();
            return false;
        }
    }

    // Validate at least one item
    const itemRows = document.querySelectorAll('.item-row');
    let hasValidItem = false;

    for (let row of itemRows) {
        const description = row.querySelector('input[name="itemDescription[]"]').value;
        const quantity = parseFloat(row.querySelector('input[name="itemQuantity[]"]').value) || 0;
        const price = parseFloat(row.querySelector('input[name="itemPrice[]"]').value) || 0;

        if (description && quantity > 0 && price >= 0) {
            hasValidItem = true;
            break;
        }
    }

    if (!hasValidItem) {
        showStatusMessage('Please add at least one valid item with description, quantity, and price', 'error');
        return false;
    }

    return true;
}

// Send Invoice function - integrated with backend
function sendInvoice() {
    if (!validateForm()) {
        return;
    }

    // Show email configuration modal
    showEmailConfigModal();
}

function showEmailConfigModal() {
    // Create modal HTML
    const modalHTML = `
        <div id="emailModal" class="email-modal">
            <div class="email-modal-content">
                <div class="email-modal-header">
                    <h3>📧 Send Invoice via Email</h3>
                    <span class="email-modal-close" onclick="closeEmailModal()">&times;</span>
                </div>
                <div class="email-modal-body">
                    <div class="email-form-group">
                        <label for="senderEmail">Your Email Address *</label>
                        <input type="email" id="senderEmail" placeholder="<EMAIL>" required>
                        <small>Use your Gmail address</small>
                    </div>
                    <div class="email-form-group">
                        <label for="senderPassword">App Password *</label>
                        <input type="password" id="senderPassword" placeholder="16-character app password" required>
                        <small><a href="https://support.google.com/accounts/answer/185833" target="_blank">How to create Gmail App Password</a></small>
                    </div>
                    <div class="email-form-group">
                        <label for="recipientEmail">Send to (Client Email) *</label>
                        <input type="email" id="recipientEmail" required>
                    </div>
                    <div class="email-form-actions">
                        <button type="button" onclick="closeEmailModal()" class="btn-cancel">Cancel</button>
                        <button type="button" onclick="sendEmailInvoice()" class="btn-send">Send Invoice</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Pre-fill recipient email from form
    const clientEmail = document.getElementById('clientEmail').value;
    if (clientEmail) {
        document.getElementById('recipientEmail').value = clientEmail;
    }

    // Show modal
    document.getElementById('emailModal').style.display = 'flex';
}

function closeEmailModal() {
    const modal = document.getElementById('emailModal');
    if (modal) {
        modal.remove();
    }
}

async function sendEmailInvoice() {
    const senderEmail = document.getElementById('senderEmail').value;
    const senderPassword = document.getElementById('senderPassword').value;
    const recipientEmail = document.getElementById('recipientEmail').value;

    // Validate email fields
    if (!senderEmail || !senderPassword || !recipientEmail) {
        showStatusMessage('Please fill in all email fields', 'error');
        return;
    }

    // Show loading state
    const sendButton = document.querySelector('.btn-send');
    const originalText = sendButton.textContent;
    sendButton.textContent = 'Sending...';
    sendButton.disabled = true;

    try {
        // Prepare invoice data
        const formData = getFormData();
        const invoiceData = {
            business_name: formData.businessName,
            business_email: formData.businessEmail,
            client_name: formData.clientName,
            client_email: formData.clientEmail,
            invoice_date: formData.invoiceDate,
            due_date: formData.dueDate,
            notes: formData.notes,
            bank_name: formData.bankName,
            account_number: formData.accountNumber,
            tax_rate: formData.taxRate,
            signature_name: formData.signatureName,
            items: formData.items,
            invoice_number: document.getElementById('previewInvoiceNumber').textContent
        };

        // Send email request to backend
        const response = await fetch('/send-invoice', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                sender_email: senderEmail,
                sender_password: senderPassword,
                recipient_email: recipientEmail,
                invoice_data: invoiceData
            })
        });

        const result = await response.json();

        if (result.success) {
            showStatusMessage(result.message, 'success');
            closeEmailModal();
        } else {
            showStatusMessage(result.message, 'error');
        }

    } catch (error) {
        console.error('Error sending email:', error);
        showStatusMessage('Failed to send email. Please check your connection and try again.', 'error');
    } finally {
        // Reset button state
        sendButton.textContent = originalText;
        sendButton.disabled = false;
    }
}

// Navigation functionality
function initializeNavigation() {
    const navTabs = document.querySelectorAll('.nav-tab');
    const tabPanes = document.querySelectorAll('.tab-pane');

    navTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');

            // Remove active class from all tabs and panes
            navTabs.forEach(t => t.classList.remove('active'));
            tabPanes.forEach(p => p.classList.remove('active'));

            // Add active class to clicked tab and corresponding pane
            this.classList.add('active');
            document.getElementById(`${targetTab}-tab`).classList.add('active');
        });
    });
}

// Invoice History functionality
async function loadInvoiceHistory() {
    try {
        const response = await fetch('/api/invoices');
        const result = await response.json();

        if (result.success) {
            displayInvoices(result.invoices);
        } else {
            console.error('Failed to load invoices:', result.message);
        }
    } catch (error) {
        console.error('Error loading invoices:', error);
    }
}

function displayInvoices(invoices) {
    const invoicesList = document.getElementById('invoicesList');

    if (invoices.length === 0) {
        invoicesList.innerHTML = '<div class="loading">No invoices found. Create your first invoice!</div>';
        return;
    }

    invoicesList.innerHTML = invoices.map(invoice => `
        <div class="invoice-card">
            <div class="invoice-header">
                <span class="invoice-number">${invoice.invoice_number}</span>
                <span class="invoice-status status-${invoice.payment_status}">${invoice.payment_status}</span>
            </div>
            <div class="invoice-details">
                <div><strong>Client:</strong> ${invoice.client_name || 'N/A'}</div>
                <div><strong>Date:</strong> ${formatDate(invoice.invoice_date)}</div>
                <div><strong>Due:</strong> ${formatDate(invoice.due_date)}</div>
                <div><strong>Total:</strong> $${parseFloat(invoice.total_amount).toFixed(2)}</div>
            </div>
            <div class="invoice-actions">
                <button class="btn-small btn-edit" onclick="editInvoice(${invoice.id})">Edit</button>
                <button class="btn-small btn-resend" onclick="resendInvoice(${invoice.id})">Resend</button>
                <select onchange="updateInvoiceStatus(${invoice.id}, this.value)" class="btn-small">
                    <option value="">Change Status</option>
                    <option value="paid" ${invoice.payment_status === 'paid' ? 'selected' : ''}>Paid</option>
                    <option value="unpaid" ${invoice.payment_status === 'unpaid' ? 'selected' : ''}>Unpaid</option>
                    <option value="overdue" ${invoice.payment_status === 'overdue' ? 'selected' : ''}>Overdue</option>
                    <option value="cancelled" ${invoice.payment_status === 'cancelled' ? 'selected' : ''}>Cancelled</option>
                </select>
            </div>
        </div>
    `).join('');
}

async function updateInvoiceStatus(invoiceId, status) {
    if (!status) return;

    try {
        const response = await fetch(`/api/invoices/${invoiceId}/status`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ status: status })
        });

        const result = await response.json();

        if (result.success) {
            showStatusMessage(result.message, 'success');
            loadInvoiceHistory(); // Refresh the list
        } else {
            showStatusMessage(result.message, 'error');
        }
    } catch (error) {
        showStatusMessage('Error updating status', 'error');
        console.error('Error updating status:', error);
    }
}

function editInvoice(invoiceId) {
    // Switch to create tab and load invoice data
    document.querySelector('[data-tab="create"]').click();
    loadInvoiceForEdit(invoiceId);
}

async function loadInvoiceForEdit(invoiceId) {
    try {
        const response = await fetch(`/api/invoices/${invoiceId}`);
        const result = await response.json();

        if (result.success) {
            const invoice = result.invoice;

            // Populate form with invoice data
            document.getElementById('businessName').value = invoice.business_name || '';
            document.getElementById('clientName').value = invoice.client_name || '';
            document.getElementById('invoiceDate').value = invoice.invoice_date;
            document.getElementById('dueDate').value = invoice.due_date;
            document.getElementById('notes').value = invoice.notes || '';

            // Load items
            // This would need more complex logic to populate the dynamic items

            updatePreview();
            showStatusMessage('Invoice loaded for editing', 'success');
        } else {
            showStatusMessage('Error loading invoice', 'error');
        }
    } catch (error) {
        showStatusMessage('Error loading invoice', 'error');
        console.error('Error loading invoice:', error);
    }
}

function resendInvoice(invoiceId) {
    // Load invoice and trigger email modal
    loadInvoiceForEdit(invoiceId);
    setTimeout(() => {
        showEmailConfigModal();
    }, 500);
}

// Client Management functionality
async function loadClients() {
    try {
        const response = await fetch('/api/clients');
        const result = await response.json();

        if (result.success) {
            displayClients(result.clients);
        } else {
            console.error('Failed to load clients:', result.message);
        }
    } catch (error) {
        console.error('Error loading clients:', error);
    }
}

function displayClients(clients) {
    const clientsList = document.getElementById('clientsList');

    if (clients.length === 0) {
        clientsList.innerHTML = '<div class="loading">No clients found. Add your first client!</div>';
        return;
    }

    clientsList.innerHTML = clients.map(client => `
        <div class="client-card">
            <div class="client-header">
                <span class="client-name"><strong>${client.name}</strong></span>
            </div>
            <div class="client-details">
                <div><strong>Email:</strong> ${client.email}</div>
                <div><strong>Phone:</strong> ${client.phone || 'N/A'}</div>
                <div><strong>Address:</strong> ${client.address || 'N/A'}</div>
            </div>
            <div class="client-actions">
                <button class="btn-small btn-edit" onclick="editClient(${client.id})">Edit</button>
                <button class="btn-small btn-delete" onclick="deleteClient(${client.id})">Delete</button>
            </div>
        </div>
    `).join('');
}

// Add event listeners for client management
document.addEventListener('DOMContentLoaded', function() {
    const addClientBtn = document.getElementById('addClientBtn');
    if (addClientBtn) {
        addClientBtn.addEventListener('click', showAddClientModal);
    }

    const refreshHistoryBtn = document.getElementById('refreshHistoryBtn');
    if (refreshHistoryBtn) {
        refreshHistoryBtn.addEventListener('click', loadInvoiceHistory);
    }
});

function showAddClientModal() {
    // Create and show client modal (simplified version)
    const clientName = prompt('Client Name:');
    const clientEmail = prompt('Client Email:');

    if (clientName && clientEmail) {
        addClient(clientName, clientEmail);
    }
}

async function addClient(name, email, address = '', phone = '') {
    try {
        const response = await fetch('/api/clients', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                name: name,
                email: email,
                address: address,
                phone: phone
            })
        });

        const result = await response.json();

        if (result.success) {
            showStatusMessage('Client added successfully', 'success');
            loadClients(); // Refresh the list
        } else {
            showStatusMessage(result.message, 'error');
        }
    } catch (error) {
        showStatusMessage('Error adding client', 'error');
        console.error('Error adding client:', error);
    }
}

function editClient(clientId) {
    // Simplified edit - in a real app, you'd show a proper modal
    const newName = prompt('New client name:');
    const newEmail = prompt('New client email:');

    if (newName && newEmail) {
        updateClient(clientId, { name: newName, email: newEmail });
    }
}

async function updateClient(clientId, data) {
    try {
        const response = await fetch(`/api/clients/${clientId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            showStatusMessage('Client updated successfully', 'success');
            loadClients(); // Refresh the list
        } else {
            showStatusMessage(result.message, 'error');
        }
    } catch (error) {
        showStatusMessage('Error updating client', 'error');
        console.error('Error updating client:', error);
    }
}

async function deleteClient(clientId) {
    if (!confirm('Are you sure you want to delete this client?')) {
        return;
    }

    try {
        const response = await fetch(`/api/clients/${clientId}`, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (result.success) {
            showStatusMessage('Client deleted successfully', 'success');
            loadClients(); // Refresh the list
        } else {
            showStatusMessage(result.message, 'error');
        }
    } catch (error) {
        showStatusMessage('Error deleting client', 'error');
        console.error('Error deleting client:', error);
    }
}
