#!/usr/bin/env python3
"""
Smart Invoice Generator - Database Models and Setup
SQLite database for users, invoices, clients, and payment tracking
"""

import sqlite3
import hashlib
import json
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import os

class Database:
    """Database manager for Smart Invoice Generator"""
    
    def __init__(self, db_path: str = "smart_invoice.db"):
        """Initialize database connection"""
        self.db_path = db_path
        self.init_database()
    
    def get_connection(self):
        """Get database connection"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # Enable dict-like access
        return conn
    
    def init_database(self):
        """Initialize database tables"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                business_name TEXT,
                business_email TEXT,
                bank_name TEXT,
                account_number TEXT,
                signature_name TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Clients table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS clients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                name TEXT NOT NULL,
                email TEXT NOT NULL,
                address TEXT,
                phone TEXT,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE
            )
        ''')
        
        # Invoices table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                client_id INTEGER,
                invoice_number TEXT NOT NULL,
                invoice_date DATE NOT NULL,
                due_date DATE NOT NULL,
                subtotal DECIMAL(10,2) NOT NULL,
                tax_rate DECIMAL(5,2) DEFAULT 0,
                tax_amount DECIMAL(10,2) DEFAULT 0,
                total_amount DECIMAL(10,2) NOT NULL,
                payment_status TEXT DEFAULT 'unpaid',
                notes TEXT,
                items_json TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE,
                FOREIGN KEY (client_id) REFERENCES clients (id) ON DELETE SET NULL
            )
        ''')
        
        # Email logs table (track sent emails)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS email_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_id INTEGER NOT NULL,
                recipient_email TEXT NOT NULL,
                sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status TEXT DEFAULT 'sent',
                error_message TEXT,
                FOREIGN KEY (invoice_id) REFERENCES invoices (id) ON DELETE CASCADE
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def hash_password(self, password: str) -> str:
        """Hash password using SHA-256"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def verify_password(self, password: str, password_hash: str) -> bool:
        """Verify password against hash"""
        return self.hash_password(password) == password_hash
    
    # User Management
    def create_user(self, username: str, email: str, password: str, 
                   business_name: str = "", business_email: str = "") -> Tuple[bool, str]:
        """Create a new user"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # Check if user already exists
            cursor.execute("SELECT id FROM users WHERE username = ? OR email = ?", 
                         (username, email))
            if cursor.fetchone():
                return False, "Username or email already exists"
            
            # Create user
            password_hash = self.hash_password(password)
            cursor.execute('''
                INSERT INTO users (username, email, password_hash, business_name, business_email)
                VALUES (?, ?, ?, ?, ?)
            ''', (username, email, password_hash, business_name, business_email))
            
            conn.commit()
            conn.close()
            return True, "User created successfully"
            
        except Exception as e:
            return False, f"Error creating user: {str(e)}"
    
    def authenticate_user(self, username: str, password: str) -> Optional[Dict]:
        """Authenticate user and return user data"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("SELECT * FROM users WHERE username = ?", (username,))
            user = cursor.fetchone()
            
            if user and self.verify_password(password, user['password_hash']):
                # Convert Row to dict and remove password hash
                user_dict = dict(user)
                del user_dict['password_hash']
                conn.close()
                return user_dict
            
            conn.close()
            return None
            
        except Exception as e:
            print(f"Authentication error: {e}")
            return None
    
    def get_user_by_id(self, user_id: int) -> Optional[Dict]:
        """Get user by ID"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("SELECT * FROM users WHERE id = ?", (user_id,))
            user = cursor.fetchone()
            
            if user:
                user_dict = dict(user)
                del user_dict['password_hash']
                conn.close()
                return user_dict
            
            conn.close()
            return None
            
        except Exception as e:
            print(f"Error getting user: {e}")
            return None
    
    def update_user_profile(self, user_id: int, **kwargs) -> Tuple[bool, str]:
        """Update user profile"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # Build update query dynamically
            allowed_fields = ['business_name', 'business_email', 'bank_name', 
                            'account_number', 'signature_name']
            updates = []
            values = []
            
            for field, value in kwargs.items():
                if field in allowed_fields:
                    updates.append(f"{field} = ?")
                    values.append(value)
            
            if not updates:
                return False, "No valid fields to update"
            
            values.append(user_id)
            query = f"UPDATE users SET {', '.join(updates)}, updated_at = CURRENT_TIMESTAMP WHERE id = ?"
            
            cursor.execute(query, values)
            conn.commit()
            conn.close()
            
            return True, "Profile updated successfully"

        except Exception as e:
            return False, f"Error updating profile: {str(e)}"

    # Client Management
    def create_client(self, user_id: int, name: str, email: str,
                     address: str = "", phone: str = "", notes: str = "") -> Tuple[bool, str, int]:
        """Create a new client"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO clients (user_id, name, email, address, phone, notes)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (user_id, name, email, address, phone, notes))

            client_id = cursor.lastrowid
            conn.commit()
            conn.close()

            return True, "Client created successfully", client_id

        except Exception as e:
            return False, f"Error creating client: {str(e)}", 0

    def get_user_clients(self, user_id: int) -> List[Dict]:
        """Get all clients for a user"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM clients
                WHERE user_id = ?
                ORDER BY name ASC
            ''', (user_id,))

            clients = [dict(row) for row in cursor.fetchall()]
            conn.close()
            return clients

        except Exception as e:
            print(f"Error getting clients: {e}")
            return []

    def get_client_by_id(self, client_id: int, user_id: int) -> Optional[Dict]:
        """Get client by ID (with user verification)"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM clients
                WHERE id = ? AND user_id = ?
            ''', (client_id, user_id))

            client = cursor.fetchone()
            conn.close()

            return dict(client) if client else None

        except Exception as e:
            print(f"Error getting client: {e}")
            return None

    def update_client(self, client_id: int, user_id: int, **kwargs) -> Tuple[bool, str]:
        """Update client information"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # Build update query
            allowed_fields = ['name', 'email', 'address', 'phone', 'notes']
            updates = []
            values = []

            for field, value in kwargs.items():
                if field in allowed_fields:
                    updates.append(f"{field} = ?")
                    values.append(value)

            if not updates:
                return False, "No valid fields to update"

            values.extend([client_id, user_id])
            query = f'''
                UPDATE clients
                SET {', '.join(updates)}, updated_at = CURRENT_TIMESTAMP
                WHERE id = ? AND user_id = ?
            '''

            cursor.execute(query, values)
            conn.commit()
            conn.close()

            return True, "Client updated successfully"

        except Exception as e:
            return False, f"Error updating client: {str(e)}"

    def delete_client(self, client_id: int, user_id: int) -> Tuple[bool, str]:
        """Delete a client"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                DELETE FROM clients
                WHERE id = ? AND user_id = ?
            ''', (client_id, user_id))

            if cursor.rowcount > 0:
                conn.commit()
                conn.close()
                return True, "Client deleted successfully"
            else:
                conn.close()
                return False, "Client not found"

        except Exception as e:
            return False, f"Error deleting client: {str(e)}"

    # Invoice Management
    def create_invoice(self, user_id: int, invoice_data: Dict) -> Tuple[bool, str, int]:
        """Create a new invoice"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            # Extract data
            client_id = invoice_data.get('client_id')
            invoice_number = invoice_data['invoice_number']
            invoice_date = invoice_data['invoice_date']
            due_date = invoice_data['due_date']
            subtotal = float(invoice_data['subtotal'])
            tax_rate = float(invoice_data.get('tax_rate', 0))
            tax_amount = float(invoice_data.get('tax_amount', 0))
            total_amount = float(invoice_data['total_amount'])
            notes = invoice_data.get('notes', '')
            items = json.dumps(invoice_data['items'])

            cursor.execute('''
                INSERT INTO invoices (
                    user_id, client_id, invoice_number, invoice_date, due_date,
                    subtotal, tax_rate, tax_amount, total_amount, notes, items_json
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (user_id, client_id, invoice_number, invoice_date, due_date,
                  subtotal, tax_rate, tax_amount, total_amount, notes, items))

            invoice_id = cursor.lastrowid
            conn.commit()
            conn.close()

            return True, "Invoice created successfully", invoice_id

        except Exception as e:
            return False, f"Error creating invoice: {str(e)}", 0

    def get_user_invoices(self, user_id: int, limit: int = 50) -> List[Dict]:
        """Get all invoices for a user"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT i.*, c.name as client_name, c.email as client_email
                FROM invoices i
                LEFT JOIN clients c ON i.client_id = c.id
                WHERE i.user_id = ?
                ORDER BY i.created_at DESC
                LIMIT ?
            ''', (user_id, limit))

            invoices = []
            for row in cursor.fetchall():
                invoice = dict(row)
                # Parse items JSON
                invoice['items'] = json.loads(invoice['items_json'])
                del invoice['items_json']
                invoices.append(invoice)

            conn.close()
            return invoices

        except Exception as e:
            print(f"Error getting invoices: {e}")
            return []

    def get_invoice_by_id(self, invoice_id: int, user_id: int) -> Optional[Dict]:
        """Get invoice by ID (with user verification)"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT i.*, c.name as client_name, c.email as client_email
                FROM invoices i
                LEFT JOIN clients c ON i.client_id = c.id
                WHERE i.id = ? AND i.user_id = ?
            ''', (invoice_id, user_id))

            row = cursor.fetchone()
            if row:
                invoice = dict(row)
                invoice['items'] = json.loads(invoice['items_json'])
                del invoice['items_json']
                conn.close()
                return invoice

            conn.close()
            return None

        except Exception as e:
            print(f"Error getting invoice: {e}")
            return None

    def update_invoice_payment_status(self, invoice_id: int, user_id: int,
                                    status: str) -> Tuple[bool, str]:
        """Update invoice payment status"""
        try:
            valid_statuses = ['paid', 'unpaid', 'overdue', 'cancelled']
            if status not in valid_statuses:
                return False, f"Invalid status. Must be one of: {', '.join(valid_statuses)}"

            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                UPDATE invoices
                SET payment_status = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ? AND user_id = ?
            ''', (status, invoice_id, user_id))

            if cursor.rowcount > 0:
                conn.commit()
                conn.close()
                return True, f"Invoice status updated to {status}"
            else:
                conn.close()
                return False, "Invoice not found"

        except Exception as e:
            return False, f"Error updating payment status: {str(e)}"

    def delete_invoice(self, invoice_id: int, user_id: int) -> Tuple[bool, str]:
        """Delete an invoice"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                DELETE FROM invoices
                WHERE id = ? AND user_id = ?
            ''', (invoice_id, user_id))

            if cursor.rowcount > 0:
                conn.commit()
                conn.close()
                return True, "Invoice deleted successfully"
            else:
                conn.close()
                return False, "Invoice not found"

        except Exception as e:
            return False, f"Error deleting invoice: {str(e)}"

    # Email Logging
    def log_email_sent(self, invoice_id: int, recipient_email: str,
                      status: str = "sent", error_message: str = None) -> bool:
        """Log email sending activity"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO email_logs (invoice_id, recipient_email, status, error_message)
                VALUES (?, ?, ?, ?)
            ''', (invoice_id, recipient_email, status, error_message))

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            print(f"Error logging email: {e}")
            return False

    def get_invoice_email_history(self, invoice_id: int) -> List[Dict]:
        """Get email history for an invoice"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute('''
                SELECT * FROM email_logs
                WHERE invoice_id = ?
                ORDER BY sent_at DESC
            ''', (invoice_id,))

            logs = [dict(row) for row in cursor.fetchall()]
            conn.close()
            return logs

        except Exception as e:
            print(f"Error getting email history: {e}")
            return []
