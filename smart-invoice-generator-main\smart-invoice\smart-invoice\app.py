#!/usr/bin/env python3
"""
Smart Invoice Generator - Flask Backend
A lightweight web application for generating professional invoices
"""

from flask import Flask, render_template, request, jsonify, session, redirect, url_for
import os
import json
from invoice_utils import EmailService, validate_email_config, generate_invoice_number
from auth import AuthManager, login_required, get_current_user_id, get_current_user
from database import Database

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-change-in-production-' + os.urandom(24).hex()

# Initialize services
auth_manager = AuthManager()
db = Database()

@app.route('/')
def index():
    """Main page - Invoice form and preview"""
    if not session.get('logged_in'):
        return redirect(url_for('login'))

    user = get_current_user()
    return render_template('index.html', user=user)

@app.route('/login')
def login():
    """Login page"""
    if session.get('logged_in'):
        return redirect(url_for('index'))
    return render_template('login.html')

@app.route('/register')
def register():
    """Registration page"""
    if session.get('logged_in'):
        return redirect(url_for('index'))
    return render_template('register.html')

@app.route('/logout')
def logout():
    """Logout user"""
    auth_manager.logout_user()
    return redirect(url_for('login'))

@app.route('/api/login', methods=['POST'])
def api_login():
    """API endpoint for user login"""
    try:
        data = request.get_json()
        username = data.get('username', '').strip()
        password = data.get('password', '').strip()

        if not username or not password:
            return jsonify({
                'success': False,
                'message': 'Username and password are required'
            }), 400

        success, message, user = auth_manager.login_user(username, password)

        if success:
            return jsonify({
                'success': True,
                'message': message,
                'user': user
            })
        else:
            return jsonify({
                'success': False,
                'message': message
            }), 401

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Login error: {str(e)}'
        }), 500

@app.route('/api/register', methods=['POST'])
def api_register():
    """API endpoint for user registration"""
    try:
        data = request.get_json()
        username = data.get('username', '').strip()
        email = data.get('email', '').strip()
        password = data.get('password', '').strip()
        business_name = data.get('business_name', '').strip()
        business_email = data.get('business_email', '').strip()

        if not username or not email or not password:
            return jsonify({
                'success': False,
                'message': 'Username, email, and password are required'
            }), 400

        success, message = auth_manager.register_user(
            username, email, password, business_name, business_email
        )

        if success:
            return jsonify({
                'success': True,
                'message': message
            })
        else:
            return jsonify({
                'success': False,
                'message': message
            }), 400

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Registration error: {str(e)}'
        }), 500

@app.route('/send-invoice', methods=['POST'])
@login_required
def send_invoice():
    """Handle invoice email sending"""
    try:
        # Get form data
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'message': 'No data received'
            }), 400

        # Validate required fields
        required_fields = ['sender_email', 'sender_password', 'recipient_email', 'invoice_data']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'message': f'Missing required field: {field}'
                }), 400

        # Validate email configuration
        is_valid, validation_message = validate_email_config(
            data['sender_email'],
            data['sender_password']
        )

        if not is_valid:
            return jsonify({
                'success': False,
                'message': validation_message
            }), 400

        # Prepare invoice data
        invoice_data = data['invoice_data']

        # Generate invoice number if not provided
        if 'invoice_number' not in invoice_data:
            invoice_data['invoice_number'] = generate_invoice_number()

        # Calculate total amount
        total_amount = 0
        if 'items' in invoice_data:
            for item in invoice_data['items']:
                quantity = float(item.get('quantity', 0))
                price = float(item.get('price', 0))
                total_amount += quantity * price

        # Add tax if specified
        tax_rate = float(invoice_data.get('tax_rate', 0)) / 100
        tax_amount = total_amount * tax_rate
        final_total = total_amount + tax_amount

        invoice_data['total_amount'] = f"{final_total:.2f}"

        # Initialize email service
        email_service = EmailService()

        # Send email (without PDF attachment for now)
        success, message = email_service.send_invoice_email(
            sender_email=data['sender_email'],
            sender_password=data['sender_password'],
            recipient_email=data['recipient_email'],
            invoice_data=invoice_data,
            pdf_content=None  # PDF attachment will be added later
        )

        if success:
            return jsonify({
                'success': True,
                'message': message,
                'invoice_number': invoice_data['invoice_number']
            })
        else:
            return jsonify({
                'success': False,
                'message': message
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error processing request: {str(e)}'
        }), 500

@app.route('/validate-email', methods=['POST'])
def validate_email():
    """Validate email configuration"""
    try:
        data = request.get_json()

        if not data or 'email' not in data or 'password' not in data:
            return jsonify({
                'success': False,
                'message': 'Email and password are required'
            }), 400

        is_valid, message = validate_email_config(data['email'], data['password'])

        return jsonify({
            'success': is_valid,
            'message': message
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error validating email: {str(e)}'
        }), 500

# Invoice Management Routes
@app.route('/api/invoices', methods=['GET'])
@login_required
def get_invoices():
    """Get all invoices for the current user"""
    try:
        user_id = get_current_user_id()
        invoices = db.get_user_invoices(user_id)

        return jsonify({
            'success': True,
            'invoices': invoices
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error getting invoices: {str(e)}'
        }), 500

@app.route('/api/invoices', methods=['POST'])
@login_required
def create_invoice():
    """Create a new invoice"""
    try:
        user_id = get_current_user_id()
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'message': 'No data received'
            }), 400

        # Generate invoice number if not provided
        if 'invoice_number' not in data:
            data['invoice_number'] = generate_invoice_number()

        # Calculate totals
        subtotal = 0
        if 'items' in data:
            for item in data['items']:
                quantity = float(item.get('quantity', 0))
                price = float(item.get('price', 0))
                subtotal += quantity * price

        tax_rate = float(data.get('tax_rate', 0))
        tax_amount = subtotal * (tax_rate / 100)
        total_amount = subtotal + tax_amount

        invoice_data = {
            'client_id': data.get('client_id'),
            'invoice_number': data['invoice_number'],
            'invoice_date': data['invoice_date'],
            'due_date': data['due_date'],
            'subtotal': subtotal,
            'tax_rate': tax_rate,
            'tax_amount': tax_amount,
            'total_amount': total_amount,
            'notes': data.get('notes', ''),
            'items': data['items']
        }

        success, message, invoice_id = db.create_invoice(user_id, invoice_data)

        if success:
            return jsonify({
                'success': True,
                'message': message,
                'invoice_id': invoice_id,
                'invoice_number': data['invoice_number']
            })
        else:
            return jsonify({
                'success': False,
                'message': message
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error creating invoice: {str(e)}'
        }), 500

@app.route('/api/invoices/<int:invoice_id>', methods=['GET'])
@login_required
def get_invoice(invoice_id):
    """Get a specific invoice"""
    try:
        user_id = get_current_user_id()
        invoice = db.get_invoice_by_id(invoice_id, user_id)

        if invoice:
            return jsonify({
                'success': True,
                'invoice': invoice
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Invoice not found'
            }), 404

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error getting invoice: {str(e)}'
        }), 500

@app.route('/api/invoices/<int:invoice_id>/status', methods=['PUT'])
@login_required
def update_invoice_status(invoice_id):
    """Update invoice payment status"""
    try:
        user_id = get_current_user_id()
        data = request.get_json()

        if not data or 'status' not in data:
            return jsonify({
                'success': False,
                'message': 'Status is required'
            }), 400

        success, message = db.update_invoice_payment_status(
            invoice_id, user_id, data['status']
        )

        if success:
            return jsonify({
                'success': True,
                'message': message
            })
        else:
            return jsonify({
                'success': False,
                'message': message
            }), 400

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error updating status: {str(e)}'
        }), 500

# Client Management Routes
@app.route('/api/clients', methods=['GET'])
@login_required
def get_clients():
    """Get all clients for the current user"""
    try:
        user_id = get_current_user_id()
        clients = db.get_user_clients(user_id)

        return jsonify({
            'success': True,
            'clients': clients
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error getting clients: {str(e)}'
        }), 500

@app.route('/api/clients', methods=['POST'])
@login_required
def create_client():
    """Create a new client"""
    try:
        user_id = get_current_user_id()
        data = request.get_json()

        if not data or 'name' not in data or 'email' not in data:
            return jsonify({
                'success': False,
                'message': 'Name and email are required'
            }), 400

        success, message, client_id = db.create_client(
            user_id=user_id,
            name=data['name'],
            email=data['email'],
            address=data.get('address', ''),
            phone=data.get('phone', ''),
            notes=data.get('notes', '')
        )

        if success:
            return jsonify({
                'success': True,
                'message': message,
                'client_id': client_id
            })
        else:
            return jsonify({
                'success': False,
                'message': message
            }), 500

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error creating client: {str(e)}'
        }), 500

@app.route('/api/clients/<int:client_id>', methods=['PUT'])
@login_required
def update_client(client_id):
    """Update a client"""
    try:
        user_id = get_current_user_id()
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'message': 'No data received'
            }), 400

        success, message = db.update_client(client_id, user_id, **data)

        if success:
            return jsonify({
                'success': True,
                'message': message
            })
        else:
            return jsonify({
                'success': False,
                'message': message
            }), 400

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error updating client: {str(e)}'
        }), 500

@app.route('/api/clients/<int:client_id>', methods=['DELETE'])
@login_required
def delete_client(client_id):
    """Delete a client"""
    try:
        user_id = get_current_user_id()
        success, message = db.delete_client(client_id, user_id)

        if success:
            return jsonify({
                'success': True,
                'message': message
            })
        else:
            return jsonify({
                'success': False,
                'message': message
            }), 404

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'Error deleting client: {str(e)}'
        }), 500

if __name__ == '__main__':
    # Run the application
    port = int(os.environ.get('PORT', 5000))
    debug = os.environ.get('FLASK_ENV') == 'development'

    print("🚀 Starting Smart Invoice Generator...")
    print(f"📱 Access the application at: http://localhost:{port}")

    app.run(host='0.0.0.0', port=port, debug=debug)
