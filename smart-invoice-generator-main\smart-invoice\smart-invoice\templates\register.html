<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Smart Invoice Generator</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <style>
        .auth-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px 0;
        }
        
        .auth-card {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            width: 100%;
            max-width: 500px;
        }
        
        .auth-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .auth-header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2rem;
        }
        
        .auth-header p {
            color: #666;
            margin: 0;
        }
        
        .auth-form {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .auth-input-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        
        .auth-input-group {
            display: flex;
            flex-direction: column;
        }
        
        .auth-input-group label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #555;
        }
        
        .auth-input-group input {
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .auth-input-group input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .auth-btn {
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s ease;
        }
        
        .auth-btn:hover {
            transform: translateY(-2px);
        }
        
        .auth-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .auth-link {
            text-align: center;
            margin-top: 20px;
        }
        
        .auth-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
        }
        
        .auth-link a:hover {
            text-decoration: underline;
        }
        
        .auth-error {
            background: #f8d7da;
            color: #721c24;
            padding: 12px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }
        
        .auth-success {
            background: #d4edda;
            color: #155724;
            padding: 12px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: none;
        }
        
        @media (max-width: 768px) {
            .auth-input-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <h1>🚀 Get Started</h1>
                <p>Create your Smart Invoice Generator account</p>
            </div>
            
            <div id="authError" class="auth-error"></div>
            <div id="authSuccess" class="auth-success"></div>
            
            <form id="registerForm" class="auth-form">
                <div class="auth-input-row">
                    <div class="auth-input-group">
                        <label for="username">Username *</label>
                        <input type="text" id="username" name="username" required>
                    </div>
                    
                    <div class="auth-input-group">
                        <label for="email">Email Address *</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                </div>
                
                <div class="auth-input-group">
                    <label for="password">Password *</label>
                    <input type="password" id="password" name="password" required>
                    <small style="color: #666; margin-top: 5px;">Minimum 6 characters</small>
                </div>
                
                <div class="auth-input-row">
                    <div class="auth-input-group">
                        <label for="businessName">Business Name</label>
                        <input type="text" id="businessName" name="businessName">
                    </div>
                    
                    <div class="auth-input-group">
                        <label for="businessEmail">Business Email</label>
                        <input type="email" id="businessEmail" name="businessEmail">
                    </div>
                </div>
                
                <button type="submit" id="registerBtn" class="auth-btn">Create Account</button>
            </form>
            
            <div class="auth-link">
                <p>Already have an account? <a href="{{ url_for('login') }}">Sign in here</a></p>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const email = document.getElementById('email').value.trim();
            const password = document.getElementById('password').value.trim();
            const businessName = document.getElementById('businessName').value.trim();
            const businessEmail = document.getElementById('businessEmail').value.trim();
            
            const registerBtn = document.getElementById('registerBtn');
            const errorDiv = document.getElementById('authError');
            const successDiv = document.getElementById('authSuccess');
            
            // Hide previous messages
            errorDiv.style.display = 'none';
            successDiv.style.display = 'none';
            
            if (!username || !email || !password) {
                showError('Please fill in all required fields');
                return;
            }
            
            if (password.length < 6) {
                showError('Password must be at least 6 characters');
                return;
            }
            
            // Show loading state
            registerBtn.textContent = 'Creating Account...';
            registerBtn.disabled = true;
            
            try {
                const response = await fetch('/api/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        email: email,
                        password: password,
                        business_name: businessName,
                        business_email: businessEmail
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showSuccess('Account created successfully! Redirecting to login...');
                    setTimeout(() => {
                        window.location.href = '/login';
                    }, 2000);
                } else {
                    showError(result.message);
                }
                
            } catch (error) {
                showError('Registration failed. Please try again.');
                console.error('Registration error:', error);
            } finally {
                registerBtn.textContent = 'Create Account';
                registerBtn.disabled = false;
            }
        });
        
        function showError(message) {
            const errorDiv = document.getElementById('authError');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }
        
        function showSuccess(message) {
            const successDiv = document.getElementById('authSuccess');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
        }
    </script>
</body>
</html>
