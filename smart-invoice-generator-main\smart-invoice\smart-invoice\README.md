# 💼 Smart Invoice Generator

[![Python](https://img.shields.io/badge/Python-3.7+-blue.svg)](https://python.org)
[![Flask](https://img.shields.io/badge/Flask-2.3+-green.svg)](https://flask.palletsprojects.com)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg)](CONTRIBUTING.md)

A lightweight, user-friendly web application that enables small business owners, freelancers, and entrepreneurs to create professional invoices directly from their browser — with no need for Word, Excel, or expensive software.

A lightweight, user-friendly web application that enables small business owners, freelancers, and entrepreneurs to create professional invoices directly from their browser — with no need for Word, Excel, or expensive software.

## 📸 Screenshots

![Smart Invoice Generator Dashboard](docs/images/dashboard.png)
*Main dashboard with tabbed interface*

![Invoice Creation](docs/images/create-invoice.png)
*Professional invoice creation with live preview*

![Invoice History](docs/images/invoice-history.png)
*Complete invoice history with payment tracking*

## 🎯 Demo

**Live Demo:** [Coming Soon]

**Test Credentials:**
- Username: `demo`
- Password: `demo123`

## 🚀 Features

### Frontend Features
- ✅ **Professional Invoice Form** - Complete form with all business details
- ✅ **Live Preview** - Real-time invoice preview as you type
- ✅ **PDF Generation** - High-quality PDF generation using jsPDF
- ✅ **Responsive Design** - Works perfectly on mobile and desktop
- ✅ **Custom Invoice Design** - Matches professional invoice templates
- ✅ **Dynamic Items** - Add/remove invoice items dynamically
- ✅ **Tax Calculations** - Automatic tax and total calculations

### Backend Features
- ✅ **User Authentication** - Simple login/register system with session management
- ✅ **Invoice History** - Save, view, edit, and re-send past invoices
- ✅ **Payment Status Tracking** - Mark invoices as paid/unpaid/overdue/cancelled
- ✅ **Client Management** - Store and manage frequently used client information
- ✅ **Email Invoice Sending** - Send invoices directly to clients via email
- ✅ **Gmail Integration** - Supports Gmail SMTP with App Passwords
- ✅ **Professional Email Templates** - HTML email templates with invoice details
- ✅ **SQLite Database** - Persistent storage for users, invoices, and clients
- ✅ **Form Validation** - Server-side validation for all inputs
- ✅ **Error Handling** - Comprehensive error handling and user feedback
- ✅ **RESTful API** - Clean API endpoints for frontend integration

## 📋 Prerequisites

- Python 3.7 or higher
- Gmail account with App Password enabled (for email sending)
- Modern web browser (Chrome, Firefox, Safari, Edge)

## 🛠️ Installation & Setup

### 1. Install Dependencies

```bash
# Navigate to the project directory
cd smart-invoice/smart-invoice

# Install Python dependencies
py -m pip install -r requirements.txt
```

### 2. Gmail App Password Setup

For email functionality, you'll need a Gmail App Password:

1. **Enable 2-Factor Authentication** on your Gmail account
2. **Generate App Password**:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate a 16-character app password
3. **Use this App Password** in the application (not your regular Gmail password)

### 3. Run the Application

```bash
# Run the Flask development server
py app.py
```

The application will be available at: `http://localhost:5000`

## 🎯 How to Use

### Creating an Invoice

1. **Open the Application** in your web browser
2. **Fill in Business Information**:
   - Business name and email
   - Bank details for payments
3. **Add Client Information**:
   - Client name and email
4. **Set Invoice Details**:
   - Invoice and due dates
   - Tax rate and signature name
5. **Add Invoice Items**:
   - Description, quantity, and price
   - Use "+" button to add more items
6. **Review Live Preview** - See your invoice update in real-time
7. **Generate PDF** - Click "Generate PDF" to download
8. **Send via Email** - Click "Send Invoice" to email directly to client

### Sending Invoices via Email

1. **Click "Send Invoice"** button
2. **Enter Email Credentials**:
   - Your Gmail address
   - Your 16-character App Password
   - Client's email address
3. **Click "Send Invoice"** - Email will be sent with professional formatting

## 📡 API Endpoints

### `POST /send-invoice`
Send an invoice via email with professional HTML template.

### `POST /validate-email`
Validate email configuration before sending.

## 🔧 Technologies Used

### Frontend
- **HTML5** - Modern semantic markup
- **CSS3** - Professional styling with gradients and animations
- **JavaScript** - Dynamic interactions and form handling
- **jsPDF** - Client-side PDF generation

### Backend
- **Python Flask** - Lightweight web framework
- **SMTP/Email** - Email sending functionality
- **HTML Email Templates** - Professional email formatting

## 📁 Project Structure

```
smart-invoice/
├── app.py                 # Main Flask application
├── invoice_utils.py       # Email and utility functions
├── requirements.txt       # Python dependencies
├── .env.example          # Environment configuration template
├── templates/
│   └── index.html        # Main application template
├── static/
│   ├── style.css         # Application styles (450+ lines)
│   └── script.js         # Frontend JavaScript (670+ lines)
├── test-frontend.html    # Standalone test file
└── README.md             # This documentation
```