#!/usr/bin/env python3
"""
Smart Invoice Generator - Email and PDF Utilities
Handles email sending functionality with PDF attachments
"""

import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders
from datetime import datetime
from typing import Dict, Tuple

class EmailService:
    """Service class for handling email operations"""

    def __init__(self):
        """Initialize email service with configuration"""
        self.smtp_server = "smtp.gmail.com"
        self.smtp_port = 587

    def send_invoice_email(self,
                          sender_email: str,
                          sender_password: str,
                          recipient_email: str,
                          invoice_data: Dict,
                          pdf_content: bytes = None) -> Tuple[bool, str]:
        """
        Send invoice email with PDF attachment

        Args:
            sender_email: Sender's email address
            sender_password: Sender's email password (app password for Gmail)
            recipient_email: Recipient's email address
            invoice_data: Dictionary containing invoice information
            pdf_content: PDF file content as bytes (optional for now)

        Returns:
            Tuple of (success: bool, message: str)
        """
        try:
            # Create message
            msg = MIMEMultipart()
            msg['From'] = sender_email
            msg['To'] = recipient_email
            msg['Subject'] = f"Invoice #{invoice_data.get('invoice_number', 'N/A')} from {invoice_data.get('business_name', 'Business')}"

            # Create email body
            body = self._create_email_body(invoice_data)
            msg.attach(MIMEText(body, 'html'))

            # Add PDF attachment if provided
            if pdf_content:
                self._attach_pdf(msg, pdf_content, invoice_data)

            # Send email
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(sender_email, sender_password)

            text = msg.as_string()
            server.sendmail(sender_email, recipient_email, text)
            server.quit()

            return True, "Invoice sent successfully!"

        except smtplib.SMTPAuthenticationError:
            return False, "Authentication failed. Please check your email credentials. For Gmail, use an App Password."
        except smtplib.SMTPRecipientsRefused:
            return False, "Invalid recipient email address."
        except smtplib.SMTPServerDisconnected:
            return False, "Connection to email server failed."
        except Exception as e:
            return False, f"Failed to send email: {str(e)}"

    def _create_email_body(self, invoice_data: Dict) -> str:
        """Create HTML email body"""
        business_name = invoice_data.get('business_name', 'Business')
        client_name = invoice_data.get('client_name', 'Client')
        invoice_number = invoice_data.get('invoice_number', 'N/A')
        total_amount = invoice_data.get('total_amount', '0.00')
        due_date = invoice_data.get('due_date', 'N/A')

        return f"""
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <h2 style="color: #667eea;">Invoice from {business_name}</h2>

                <p>Dear {client_name},</p>

                <p>Please find attached your invoice with the following details:</p>

                <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
                    <p><strong>Invoice Number:</strong> {invoice_number}</p>
                    <p><strong>Total Amount:</strong> ${total_amount}</p>
                    <p><strong>Due Date:</strong> {due_date}</p>
                </div>

                <p>Please review the attached invoice and process payment by the due date.</p>

                <p>If you have any questions about this invoice, please don't hesitate to contact us.</p>

                <p>Thank you for your business!</p>

                <p>Best regards,<br>
                {business_name}</p>

                <hr style="margin-top: 30px; border: none; border-top: 1px solid #eee;">
                <p style="font-size: 12px; color: #666;">
                    This invoice was generated using Smart Invoice Generator.
                </p>
            </div>
        </body>
        </html>
        """

    def _attach_pdf(self, msg: MIMEMultipart, pdf_content: bytes, invoice_data: Dict):
        """Attach PDF to email message"""
        # Create filename
        invoice_number = invoice_data.get('invoice_number', 'invoice')
        client_name = invoice_data.get('client_name', 'client').replace(' ', '_').lower()
        filename = f"invoice_{invoice_number}_{client_name}.pdf"

        # Attach PDF
        part = MIMEBase('application', 'octet-stream')
        part.set_payload(pdf_content)
        encoders.encode_base64(part)
        part.add_header(
            'Content-Disposition',
            f'attachment; filename= {filename}'
        )
        msg.attach(part)

def validate_email_config(email: str, password: str) -> Tuple[bool, str]:
    """
    Validate email configuration

    Args:
        email: Email address
        password: Email password

    Returns:
        Tuple of (is_valid: bool, message: str)
    """
    if not email or not password:
        return False, "Email and password are required"

    if '@' not in email:
        return False, "Invalid email format"

    # Basic Gmail check
    if 'gmail.com' in email.lower() and len(password) < 16:
        return False, "For Gmail, please use an App Password (16 characters)"

    return True, "Email configuration is valid"

def format_currency(amount: float) -> str:
    """Format currency amount"""
    return f"{amount:.2f}"

def generate_invoice_number() -> str:
    """Generate a unique invoice number"""
    now = datetime.now()
    return f"INV-{now.strftime('%Y%m%d')}-{now.strftime('%H%M%S')}"
