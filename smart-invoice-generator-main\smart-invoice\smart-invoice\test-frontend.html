<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Smart Invoice Generator - Test</title>
    <link rel="stylesheet" href="static/style.css">
    <!-- jsPDF CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>💼 Smart Invoice Generator</h1>
            <p>Create professional invoices in seconds</p>
        </header>

        <div class="main-content">
            <!-- Invoice Form Section -->
            <div class="form-section">
                <h2>Invoice Details</h2>
                <form id="invoiceForm">
                    <!-- Business Information -->
                    <div class="form-group">
                        <h3>Business Information</h3>
                        <div class="input-row">
                            <div class="input-group">
                                <label for="businessName">Business Name *</label>
                                <input type="text" id="businessName" name="businessName" required>
                            </div>
                            <div class="input-group">
                                <label for="businessEmail">Business Email *</label>
                                <input type="email" id="businessEmail" name="businessEmail" required>
                            </div>
                        </div>
                        <div class="input-row">
                            <div class="input-group">
                                <label for="bankName">Bank Name</label>
                                <input type="text" id="bankName" name="bankName" value="Borcele Bank">
                            </div>
                            <div class="input-group">
                                <label for="accountNumber">Account Number</label>
                                <input type="text" id="accountNumber" name="accountNumber" value="0123 4567 8901">
                            </div>
                        </div>
                    </div>

                    <!-- Client Information -->
                    <div class="form-group">
                        <h3>Client Information</h3>
                        <div class="input-row">
                            <div class="input-group">
                                <label for="clientName">Client Name *</label>
                                <input type="text" id="clientName" name="clientName" required>
                            </div>
                            <div class="input-group">
                                <label for="clientEmail">Client Email *</label>
                                <input type="email" id="clientEmail" name="clientEmail" required>
                            </div>
                        </div>
                    </div>

                    <!-- Invoice Dates -->
                    <div class="form-group">
                        <h3>Invoice Dates</h3>
                        <div class="input-row">
                            <div class="input-group">
                                <label for="invoiceDate">Invoice Date *</label>
                                <input type="date" id="invoiceDate" name="invoiceDate" required>
                            </div>
                            <div class="input-group">
                                <label for="dueDate">Due Date *</label>
                                <input type="date" id="dueDate" name="dueDate" required>
                            </div>
                        </div>
                    </div>

                    <!-- Invoice Items -->
                    <div class="form-group">
                        <h3>Invoice Items</h3>
                        <div id="invoiceItems">
                            <div class="item-row" data-item="1">
                                <div class="item-input-group">
                                    <label>Description *</label>
                                    <input type="text" name="itemDescription[]" required>
                                </div>
                                <div class="item-input-group">
                                    <label>Quantity *</label>
                                    <input type="number" name="itemQuantity[]" min="1" value="1" required>
                                </div>
                                <div class="item-input-group">
                                    <label>Price ($) *</label>
                                    <input type="number" name="itemPrice[]" step="0.01" min="0" required>
                                </div>
                                <div class="item-input-group">
                                    <label>Total</label>
                                    <input type="text" class="item-total" readonly>
                                </div>
                                <button type="button" class="remove-item-btn" onclick="removeItem(1)">×</button>
                            </div>
                        </div>
                        <button type="button" id="addItemBtn" class="add-item-btn">+ Add Item</button>
                    </div>

                    <!-- Invoice Settings -->
                    <div class="form-group">
                        <h3>Invoice Settings</h3>
                        <div class="input-row">
                            <div class="input-group">
                                <label for="taxRate">Tax Rate (%)</label>
                                <input type="number" id="taxRate" name="taxRate" value="10" min="0" max="100" step="0.1">
                            </div>
                            <div class="input-group">
                                <label for="signatureName">Signature Name</label>
                                <input type="text" id="signatureName" name="signatureName" value="Adeline Palmerston">
                            </div>
                        </div>
                    </div>

                    <!-- Notes Section -->
                    <div class="form-group">
                        <h3>Additional Notes</h3>
                        <textarea id="notes" name="notes" rows="4" placeholder="Payment terms, additional information, etc."></textarea>
                    </div>

                    <!-- Action Buttons -->
                    <div class="action-buttons">
                        <button type="button" id="generatePdfBtn" class="btn btn-primary">📄 Generate PDF</button>
                        <button type="button" id="sendInvoiceBtn" class="btn btn-secondary">📧 Send Invoice</button>
                    </div>
                </form>
            </div>

            <!-- Live Preview Section -->
            <div class="preview-section">
                <h2>Live Preview</h2>
                <div id="invoicePreview" class="invoice-preview">
                    <div class="invoice-header">
                        <div class="header-line"></div>
                        <h1 class="invoice-title">I N V O I C E</h1>
                    </div>

                    <div class="invoice-details">
                        <div class="issued-to">
                            <h4>ISSUED TO:</h4>
                            <p id="previewClientName">Richard Sanchez</p>
                            <p id="previewBusinessName">Thynk Unlimited</p>
                            <p id="previewClientEmail">123 Anywhere St., Any City</p>
                        </div>

                        <div class="invoice-info">
                            <p><strong>INVOICE NO:</strong> <span id="previewInvoiceNumber">01234</span></p>
                            <p><strong>DATE:</strong> <span id="previewInvoiceDate">11.02.2030</span></p>
                            <p><strong>DUE DATE:</strong> <span id="previewDueDate">11.03.2030</span></p>
                        </div>
                    </div>

                    <div class="pay-to-section">
                        <h4>PAY TO:</h4>
                        <p id="previewBankName">Borcele Bank</p>
                        <p>Account Name: <span id="previewAccountName">Adeline Palmerston</span></p>
                        <p>Account No.: <span id="previewAccountNumber">0123 4567 8901</span></p>
                    </div>

                    <div class="invoice-items">
                        <table id="previewItemsTable">
                            <thead>
                                <tr>
                                    <th>DESCRIPTION</th>
                                    <th>UNIT PRICE</th>
                                    <th>QTY</th>
                                    <th>TOTAL</th>
                                </tr>
                            </thead>
                            <tbody id="previewItemsBody">
                                <tr>
                                    <td>Brand consultation</td>
                                    <td>100</td>
                                    <td>1</td>
                                    <td>$100</td>
                                </tr>
                            </tbody>
                        </table>

                        <div class="invoice-totals">
                            <div class="totals-row">
                                <span class="totals-label">SUBTOTAL</span>
                                <span class="totals-value">$<span id="previewSubtotal">100</span></span>
                            </div>
                            <div class="totals-row">
                                <span class="totals-label">Tax</span>
                                <span class="totals-value"><span id="previewTaxRate">10</span>%</span>
                            </div>
                            <div class="totals-row total-final">
                                <span class="totals-label">TOTAL</span>
                                <span class="totals-value">$<span id="previewTotal">110</span></span>
                            </div>
                        </div>
                    </div>

                    <div class="invoice-signature">
                        <div class="signature-line"></div>
                        <p class="signature-name" id="previewSignatureName">Adeline Palmerston</p>
                    </div>

                    <div class="invoice-notes">
                        <h4>Notes:</h4>
                        <p id="previewNotes">Additional notes will appear here...</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status Messages -->
        <div id="statusMessage" class="status-message hidden"></div>
    </div>

    <script src="static/script.js"></script>
</body>
</html>
