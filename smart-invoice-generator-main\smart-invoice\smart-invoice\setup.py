#!/usr/bin/env python3
"""
Smart Invoice Generator - Setup Script
Automated setup for new installations
"""

import os
import subprocess
import sys

def install_dependencies():
    """Install required Python packages"""
    print("📦 Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install dependencies")
        return False

def initialize_database():
    """Initialize the database with sample data"""
    print("🗄️ Initializing database...")
    try:
        from database import Database
        from init_db import init_database
        
        init_database()
        print("✅ Database initialized successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to initialize database: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 Smart Invoice Generator - Setup")
    print("=" * 40)
    
    # Check Python version
    if sys.version_info < (3, 7):
        print("❌ Python 3.7 or higher is required")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    
    # Install dependencies
    if not install_dependencies():
        return False
    
    # Initialize database
    if not initialize_database():
        return False
    
    print("\n🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Run: py app.py")
    print("2. Open: http://localhost:5000")
    print("3. Login with: demo / demo123")
    print("\n💡 Or create a new account!")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
