# Smart Invoice Generator - Environment Configuration
# Copy this file to .env and fill in your actual values

# Flask Configuration
FLASK_ENV=development
FLASK_SECRET_KEY=your-secret-key-change-in-production

# Email Configuration (Optional - users can enter these in the UI)
DEFAULT_SMTP_SERVER=smtp.gmail.com
DEFAULT_SMTP_PORT=587

# Application Settings
APP_NAME=Smart Invoice Generator
APP_VERSION=1.0.0

# Security Settings
MAX_EMAIL_ATTEMPTS=5
EMAIL_TIMEOUT_SECONDS=30

# File Upload Settings (for future PDF attachment feature)
MAX_FILE_SIZE_MB=10
ALLOWED_FILE_EXTENSIONS=pdf,png,jpg,jpeg

# Development Settings
DEBUG_MODE=True
LOG_LEVEL=INFO
