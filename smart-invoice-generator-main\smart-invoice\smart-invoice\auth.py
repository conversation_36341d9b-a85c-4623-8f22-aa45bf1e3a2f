#!/usr/bin/env python3
"""
Smart Invoice Generator - Authentication Module
Simple authentication system with session management
"""

from functools import wraps
from flask import session, request, jsonify, redirect, url_for
from database import Database

class AuthManager:
    """Authentication manager for user sessions"""
    
    def __init__(self):
        self.db = Database()
    
    def login_user(self, username: str, password: str) -> tuple[bool, str, dict]:
        """Login user and create session"""
        user = self.db.authenticate_user(username, password)
        
        if user:
            # Create session
            session['user_id'] = user['id']
            session['username'] = user['username']
            session['logged_in'] = True
            
            return True, "Login successful", user
        else:
            return False, "Invalid username or password", {}
    
    def logout_user(self):
        """Logout user and clear session"""
        session.clear()
    
    def is_logged_in(self) -> bool:
        """Check if user is logged in"""
        return session.get('logged_in', False)
    
    def get_current_user_id(self) -> int:
        """Get current user ID from session"""
        return session.get('user_id', 0)
    
    def get_current_user(self) -> dict:
        """Get current user data"""
        if self.is_logged_in():
            user_id = self.get_current_user_id()
            return self.db.get_user_by_id(user_id) or {}
        return {}
    
    def register_user(self, username: str, email: str, password: str, 
                     business_name: str = "", business_email: str = "") -> tuple[bool, str]:
        """Register a new user"""
        # Basic validation
        if len(username) < 3:
            return False, "Username must be at least 3 characters"
        
        if len(password) < 6:
            return False, "Password must be at least 6 characters"
        
        if '@' not in email:
            return False, "Invalid email format"
        
        # Create user in database
        success, message = self.db.create_user(username, email, password, 
                                             business_name, business_email)
        return success, message

def login_required(f):
    """Decorator to require login for routes"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session.get('logged_in'):
            if request.is_json:
                return jsonify({'success': False, 'message': 'Login required'}), 401
            else:
                return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

def get_current_user_id():
    """Helper function to get current user ID"""
    return session.get('user_id', 0)

def get_current_user():
    """Helper function to get current user data"""
    if session.get('logged_in'):
        db = Database()
        user_id = session.get('user_id')
        return db.get_user_by_id(user_id) or {}
    return {}
