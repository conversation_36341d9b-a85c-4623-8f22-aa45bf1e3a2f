/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
header {
    margin-bottom: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-left h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    margin: 0;
}

.header-left p {
    font-size: 1.2rem;
    opacity: 0.9;
    margin: 5px 0 0 0;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.user-welcome {
    font-size: 1rem;
    opacity: 0.9;
}

.logout-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 8px 16px;
    border-radius: 5px;
    text-decoration: none;
    transition: background-color 0.3s ease;
}

.logout-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* Navigation Tabs */
.nav-tabs {
    display: flex;
    background: white;
    border-radius: 10px;
    padding: 5px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.nav-tab {
    flex: 1;
    padding: 15px 20px;
    border: none;
    background: transparent;
    color: #666;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.nav-tab:hover {
    background: #f8f9fa;
    color: #333;
}

.nav-tab.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

/* Tab Content */
.tab-content {
    min-height: 600px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
}

/* Main Content Layout */
.main-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

/* Form Section Styles */
.form-section {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.form-section h2 {
    color: #667eea;
    margin-bottom: 25px;
    font-size: 1.8rem;
}

.form-group {
    margin-bottom: 30px;
}

.form-group h3 {
    color: #555;
    margin-bottom: 15px;
    font-size: 1.2rem;
    border-bottom: 2px solid #e0e0e0;
    padding-bottom: 5px;
}

/* Input Styles */
.input-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.input-group {
    display: flex;
    flex-direction: column;
}

.input-group label {
    font-weight: 600;
    margin-bottom: 5px;
    color: #555;
}

.input-group input,
.input-group textarea {
    padding: 12px;
    border: 2px solid #e0e0e0;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.input-group input:focus,
.input-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

textarea {
    resize: vertical;
    min-height: 100px;
}

/* Invoice Items Styles */
.item-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr auto;
    gap: 10px;
    align-items: end;
    margin-bottom: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 5px;
    border: 1px solid #e0e0e0;
}

.item-input-group {
    display: flex;
    flex-direction: column;
}

.item-input-group label {
    font-weight: 600;
    margin-bottom: 5px;
    color: #555;
    font-size: 0.9rem;
}

.item-input-group input {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 0.9rem;
}

.item-total {
    background-color: #f0f0f0 !important;
    font-weight: bold;
}

.remove-item-btn {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s ease;
}

.remove-item-btn:hover {
    background: #c82333;
}

.add-item-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    margin-top: 10px;
    transition: background-color 0.3s ease;
}

.add-item-btn:hover {
    background: #218838;
}

/* Button Styles */
.action-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
}

.btn {
    padding: 15px 30px;
    border: none;
    border-radius: 5px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

/* Preview Section Styles */
.preview-section {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.preview-section h2 {
    color: #333;
    margin-bottom: 25px;
    font-size: 1.8rem;
}

.invoice-preview {
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 30px;
    background: white;
    font-size: 0.9rem;
}

.invoice-header {
    position: relative;
    margin-bottom: 40px;
    padding: 20px 0;
}

.header-line {
    position: absolute;
    top: 20px;
    left: 0;
    width: 80px;
    height: 1px;
    background-color: #333;
}

.invoice-title {
    font-size: 2rem;
    font-weight: 300;
    letter-spacing: 8px;
    color: #333;
    text-align: right;
    margin: 0;
    padding-right: 20px;
}

.invoice-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 40px;
}

.issued-to h4,
.pay-to-section h4 {
    font-size: 0.8rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
    letter-spacing: 1px;
}

.issued-to p,
.pay-to-section p {
    margin-bottom: 6px;
    color: #333;
    font-size: 0.9rem;
}

.invoice-info {
    text-align: right;
}

.invoice-info p {
    margin-bottom: 6px;
    font-size: 0.8rem;
    color: #333;
}

.pay-to-section {
    margin-bottom: 40px;
}

.invoice-items {
    margin-bottom: 30px;
}

.invoice-items table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 30px;
}

.invoice-items th {
    padding: 12px 8px;
    text-align: left;
    border-bottom: 1px solid #333;
    font-weight: bold;
    color: #333;
    font-size: 0.8rem;
    letter-spacing: 1px;
    background: none;
}

.invoice-items td {
    padding: 12px 8px;
    text-align: left;
    border: none;
    color: #333;
    font-size: 0.9rem;
}

.invoice-items th:nth-child(2),
.invoice-items th:nth-child(3),
.invoice-items th:nth-child(4),
.invoice-items td:nth-child(2),
.invoice-items td:nth-child(3),
.invoice-items td:nth-child(4) {
    text-align: right;
}

.invoice-totals {
    margin-top: 30px;
}

.totals-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.totals-row.total-final {
    font-weight: bold;
    font-size: 1rem;
    margin-top: 10px;
}

.totals-label {
    color: #333;
}

.totals-value {
    color: #333;
    text-align: right;
}

.invoice-signature {
    margin: 40px 0;
    text-align: right;
}

.signature-line {
    width: 120px;
    height: 1px;
    background-color: #333;
    margin-left: auto;
    margin-bottom: 5px;
}

.signature-name {
    font-size: 1.2rem;
    color: #333;
    font-style: italic;
    margin: 0;
}

.invoice-notes {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e0e0e0;
}

.invoice-notes h4 {
    color: #333;
    margin-bottom: 10px;
    font-size: 0.9rem;
}

.invoice-notes p {
    color: #666;
    font-style: italic;
    font-size: 0.8rem;
}

/* Status Message Styles */
.status-message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 5px;
    font-weight: 600;
    z-index: 1000;
    transition: all 0.3s ease;
}

.status-message.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-message.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status-message.hidden {
    display: none;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .container {
        padding: 15px;
    }

    header h1 {
        font-size: 2rem;
    }

    header p {
        font-size: 1rem;
    }
}

@media (max-width: 768px) {
    .input-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .item-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .item-row .remove-item-btn {
        justify-self: end;
        margin-top: 10px;
    }

    .action-buttons {
        flex-direction: column;
        gap: 10px;
    }

    .btn {
        padding: 12px 20px;
        font-size: 1rem;
    }

    .invoice-details {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .form-section,
    .preview-section {
        padding: 20px;
    }

    header {
        padding: 20px;
    }

    header h1 {
        font-size: 1.8rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 10px;
    }

    .invoice-preview {
        padding: 20px;
        font-size: 0.8rem;
    }

    .invoice-items th,
    .invoice-items td {
        padding: 8px 4px;
        font-size: 0.8rem;
    }

    .form-section,
    .preview-section {
        padding: 15px;
    }

    .header-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .header-right {
        justify-content: center;
    }

    .nav-tabs {
        flex-wrap: wrap;
    }

    .nav-tab {
        font-size: 0.9rem;
        padding: 12px 15px;
    }
}

/* History Tab Styles */
.history-content, .clients-content, .profile-content {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.history-header, .clients-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
}

.history-header h2, .clients-header h2 {
    color: #667eea;
    margin: 0;
    font-size: 1.8rem;
}

.history-filters {
    margin-bottom: 20px;
}

.filter-select {
    padding: 10px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 5px;
    font-size: 1rem;
    background: white;
    cursor: pointer;
}

.invoices-list, .clients-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.invoice-card, .client-card {
    background: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 20px;
    transition: box-shadow 0.3s ease;
}

.invoice-card:hover, .client-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.invoice-header, .client-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.invoice-number {
    font-weight: bold;
    color: #667eea;
    font-size: 1.1rem;
}

.invoice-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-paid {
    background: #d4edda;
    color: #155724;
}

.status-unpaid {
    background: #fff3cd;
    color: #856404;
}

.status-overdue {
    background: #f8d7da;
    color: #721c24;
}

.status-cancelled {
    background: #e2e3e5;
    color: #6c757d;
}

.invoice-details, .client-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.invoice-actions, .client-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.btn-small {
    padding: 6px 12px;
    font-size: 0.85rem;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-edit {
    background: #ffc107;
    color: #212529;
}

.btn-edit:hover {
    background: #e0a800;
}

.btn-delete {
    background: #dc3545;
    color: white;
}

.btn-delete:hover {
    background: #c82333;
}

.btn-resend {
    background: #28a745;
    color: white;
}

.btn-resend:hover {
    background: #218838;
}

.loading {
    text-align: center;
    padding: 40px;
    color: #666;
    font-style: italic;
}

/* Profile Form Styles */
.profile-form {
    max-width: 800px;
}

.profile-form .form-group {
    margin-bottom: 30px;
}

/* Email Modal Styles */
.email-modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    justify-content: center;
    align-items: center;
}

.email-modal-content {
    background-color: white;
    border-radius: 10px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.email-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid #e0e0e0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px 10px 0 0;
}

.email-modal-header h3 {
    margin: 0;
    font-size: 1.3rem;
}

.email-modal-close {
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: white;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.email-modal-close:hover {
    opacity: 1;
}

.email-modal-body {
    padding: 25px;
}

.email-form-group {
    margin-bottom: 20px;
}

.email-form-group label {
    display: block;
    font-weight: 600;
    margin-bottom: 5px;
    color: #555;
}

.email-form-group input {
    width: 100%;
    padding: 12px;
    border: 2px solid #e0e0e0;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.email-form-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.email-form-group small {
    display: block;
    margin-top: 5px;
    color: #666;
    font-size: 0.85rem;
}

.email-form-group small a {
    color: #667eea;
    text-decoration: none;
}

.email-form-group small a:hover {
    text-decoration: underline;
}

.email-form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e0e0e0;
}

.btn-cancel {
    padding: 12px 25px;
    border: 2px solid #ddd;
    background: white;
    color: #666;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.btn-cancel:hover {
    border-color: #bbb;
    color: #555;
}

.btn-send {
    padding: 12px 25px;
    border: none;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-send:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.btn-send:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Mobile responsiveness for modal */
@media (max-width: 768px) {
    .email-modal-content {
        width: 95%;
        margin: 10px;
    }

    .email-modal-header,
    .email-modal-body {
        padding: 20px;
    }

    .email-form-actions {
        flex-direction: column;
    }

    .btn-cancel,
    .btn-send {
        width: 100%;
    }
}