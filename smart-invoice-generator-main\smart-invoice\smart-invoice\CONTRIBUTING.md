# Contributing to Smart Invoice Generator

Thank you for your interest in contributing to Smart Invoice Generator! We welcome contributions from the community.

## 🚀 Getting Started

1. Fork the repository
2. Clone your fork: `git clone https://github.com/YOUR_USERNAME/smart-invoice-generator.git`
3. Create a new branch: `git checkout -b feature/your-feature-name`
4. Make your changes
5. Test your changes thoroughly
6. Commit your changes: `git commit -m "Add your feature"`
7. Push to your branch: `git push origin feature/your-feature-name`
8. Create a Pull Request

## 🛠️ Development Setup

1. **Install Dependencies:**
   ```bash
   py -m pip install -r requirements.txt
   ```

2. **Initialize Database:**
   ```bash
   py init_db.py
   ```

3. **Run the Application:**
   ```bash
   py app.py
   ```

## 📋 Code Style

- Follow PEP 8 for Python code
- Use meaningful variable and function names
- Add comments for complex logic
- Write docstrings for functions and classes

## 🧪 Testing

- Test your changes thoroughly
- Ensure all existing functionality still works
- Add tests for new features when possible

## 📝 Pull Request Guidelines

- Provide a clear description of the changes
- Reference any related issues
- Include screenshots for UI changes
- Ensure your code follows the project's style guidelines

## 🐛 Bug Reports

When reporting bugs, please include:
- Steps to reproduce the issue
- Expected behavior
- Actual behavior
- Screenshots (if applicable)
- Your environment (OS, Python version, etc.)

## 💡 Feature Requests

We welcome feature requests! Please:
- Check if the feature already exists
- Provide a clear description of the feature
- Explain why it would be useful
- Consider contributing the feature yourself

## 📞 Questions?

If you have questions, feel free to:
- Open an issue for discussion
- Contact the maintainers

Thank you for contributing! 🎉
