#!/usr/bin/env python3
"""
Initialize the Smart Invoice Generator database
Run this script to set up the database with sample data
"""

from database import Database
from datetime import datetime, timedelta
import json

def init_database():
    """Initialize database and create sample data"""
    print("🗄️ Initializing Smart Invoice Generator Database...")
    
    # Initialize database
    db = Database()
    print("✅ Database tables created successfully")
    
    # Create sample user
    success, message = db.create_user(
        username="demo",
        email="<EMAIL>",
        password="demo123",
        business_name="Demo Business",
        business_email="<EMAIL>"
    )
    
    if success:
        print("✅ Sample user created: username='demo', password='demo123'")
    else:
        print(f"ℹ️ User creation: {message}")
    
    # Get user for sample data
    user = db.authenticate_user("demo", "demo123")
    if not user:
        print("❌ Could not authenticate sample user")
        return
    
    user_id = user['id']
    
    # Create sample clients
    clients_data = [
        {
            'name': '<PERSON>',
            'email': '<EMAIL>',
            'address': '123 Main St, City, State',
            'phone': '******-0123'
        },
        {
            'name': '<PERSON>',
            'email': '<EMAIL>',
            'address': '456 Business Ave, City, State',
            'phone': '******-0456'
        },
        {
            'name': 'Tech Solutions Inc',
            'email': '<EMAIL>',
            'address': '789 Innovation Blvd, Tech City',
            'phone': '******-0789'
        }
    ]
    
    client_ids = []
    for client_data in clients_data:
        success, message, client_id = db.create_client(user_id, **client_data)
        if success:
            client_ids.append(client_id)
            print(f"✅ Created client: {client_data['name']}")
        else:
            print(f"❌ Failed to create client {client_data['name']}: {message}")
    
    # Create sample invoices
    sample_invoices = [
        {
            'client_id': client_ids[0] if client_ids else None,
            'invoice_number': 'INV-2024-001',
            'invoice_date': (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'),
            'due_date': (datetime.now() - timedelta(days=15)).strftime('%Y-%m-%d'),
            'subtotal': 500.00,
            'tax_rate': 10.0,
            'tax_amount': 50.00,
            'total_amount': 550.00,
            'notes': 'Thank you for your business!',
            'items': [
                {'description': 'Web Development', 'quantity': 1, 'price': 300.00},
                {'description': 'Logo Design', 'quantity': 1, 'price': 200.00}
            ]
        },
        {
            'client_id': client_ids[1] if len(client_ids) > 1 else None,
            'invoice_number': 'INV-2024-002',
            'invoice_date': (datetime.now() - timedelta(days=15)).strftime('%Y-%m-%d'),
            'due_date': (datetime.now() + timedelta(days=15)).strftime('%Y-%m-%d'),
            'subtotal': 750.00,
            'tax_rate': 10.0,
            'tax_amount': 75.00,
            'total_amount': 825.00,
            'notes': 'Payment due within 30 days',
            'items': [
                {'description': 'Consulting Services', 'quantity': 5, 'price': 150.00}
            ]
        },
        {
            'client_id': client_ids[2] if len(client_ids) > 2 else None,
            'invoice_number': 'INV-2024-003',
            'invoice_date': datetime.now().strftime('%Y-%m-%d'),
            'due_date': (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d'),
            'subtotal': 1200.00,
            'tax_rate': 10.0,
            'tax_amount': 120.00,
            'total_amount': 1320.00,
            'notes': 'Project milestone payment',
            'items': [
                {'description': 'Software Development', 'quantity': 1, 'price': 800.00},
                {'description': 'Testing & QA', 'quantity': 1, 'price': 400.00}
            ]
        }
    ]
    
    for i, invoice_data in enumerate(sample_invoices):
        success, message, invoice_id = db.create_invoice(user_id, invoice_data)
        if success:
            print(f"✅ Created invoice: {invoice_data['invoice_number']}")
            
            # Set different payment statuses for demo
            if i == 0:
                db.update_invoice_payment_status(invoice_id, user_id, 'paid')
            elif i == 1:
                db.update_invoice_payment_status(invoice_id, user_id, 'overdue')
            # Third invoice remains 'unpaid'
            
        else:
            print(f"❌ Failed to create invoice {invoice_data['invoice_number']}: {message}")
    
    print("\n🎉 Database initialization complete!")
    print("\n📋 Sample Data Created:")
    print("   👤 User: demo / demo123")
    print("   👥 3 Sample clients")
    print("   📄 3 Sample invoices with different statuses")
    print("\n🚀 You can now run the application with: py app.py")

if __name__ == "__main__":
    init_database()
