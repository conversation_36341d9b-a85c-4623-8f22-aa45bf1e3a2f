#!/usr/bin/env python3
"""
Test script for email functionality
Run this to test email sending without the full web interface
"""

from invoice_utils import EmailService, validate_email_config

def test_email_validation():
    """Test email validation function"""
    print("🧪 Testing Email Validation...")
    
    # Test valid Gmail configuration
    is_valid, message = validate_email_config("<EMAIL>", "abcdefghijklmnop")
    print(f"Valid Gmail config: {is_valid} - {message}")
    
    # Test invalid email
    is_valid, message = validate_email_config("invalid-email", "password")
    print(f"Invalid email: {is_valid} - {message}")
    
    # Test short password for Gmail
    is_valid, message = validate_email_config("<EMAIL>", "short")
    print(f"Short Gmail password: {is_valid} - {message}")
    
    print("✅ Email validation tests completed\n")

def test_email_service():
    """Test email service initialization"""
    print("🧪 Testing Email Service...")
    
    try:
        email_service = EmailService()
        print(f"✅ Email service initialized successfully")
        print(f"   SMTP Server: {email_service.smtp_server}")
        print(f"   SMTP Port: {email_service.smtp_port}")
    except Exception as e:
        print(f"❌ Email service initialization failed: {e}")
    
    print("✅ Email service tests completed\n")

def test_email_body_generation():
    """Test email body generation"""
    print("🧪 Testing Email Body Generation...")
    
    try:
        email_service = EmailService()
        
        # Sample invoice data
        invoice_data = {
            'business_name': 'Test Business',
            'client_name': 'Test Client',
            'invoice_number': 'INV-001',
            'total_amount': '150.00',
            'due_date': '2024-01-15'
        }
        
        body = email_service._create_email_body(invoice_data)
        
        # Check if body contains expected elements
        if 'Test Business' in body and 'Test Client' in body and 'INV-001' in body:
            print("✅ Email body generated successfully")
            print("   Contains business name, client name, and invoice number")
        else:
            print("❌ Email body missing expected content")
            
    except Exception as e:
        print(f"❌ Email body generation failed: {e}")
    
    print("✅ Email body generation tests completed\n")

def interactive_email_test():
    """Interactive email sending test"""
    print("🧪 Interactive Email Test")
    print("This will attempt to send a real test email.")
    print("You'll need valid Gmail credentials.\n")
    
    try:
        # Get user input
        sender_email = input("Enter your Gmail address: ").strip()
        sender_password = input("Enter your Gmail App Password (16 chars): ").strip()
        recipient_email = input("Enter recipient email: ").strip()
        
        if not sender_email or not sender_password or not recipient_email:
            print("❌ All fields are required")
            return
        
        # Validate configuration
        is_valid, message = validate_email_config(sender_email, sender_password)
        if not is_valid:
            print(f"❌ Email configuration invalid: {message}")
            return
        
        print("✅ Email configuration is valid")
        
        # Prepare test invoice data
        invoice_data = {
            'business_name': 'Test Business',
            'client_name': 'Test Client',
            'invoice_number': 'TEST-001',
            'total_amount': '100.00',
            'due_date': '2024-01-31'
        }
        
        # Send test email
        print("📧 Sending test email...")
        email_service = EmailService()
        success, result_message = email_service.send_invoice_email(
            sender_email=sender_email,
            sender_password=sender_password,
            recipient_email=recipient_email,
            invoice_data=invoice_data
        )
        
        if success:
            print(f"✅ {result_message}")
        else:
            print(f"❌ {result_message}")
            
    except KeyboardInterrupt:
        print("\n❌ Test cancelled by user")
    except Exception as e:
        print(f"❌ Test failed: {e}")

def main():
    """Run all tests"""
    print("🚀 Smart Invoice Generator - Email Testing Suite\n")
    
    # Run automated tests
    test_email_validation()
    test_email_service()
    test_email_body_generation()
    
    # Ask user if they want to run interactive test
    print("Would you like to run an interactive email sending test?")
    print("This requires real Gmail credentials and will send a test email.")
    choice = input("Run interactive test? (y/N): ").strip().lower()
    
    if choice in ['y', 'yes']:
        interactive_email_test()
    else:
        print("Skipping interactive test.")
    
    print("\n🎉 All tests completed!")

if __name__ == "__main__":
    main()
